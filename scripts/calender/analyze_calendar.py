#!/usr/bin/env python3
"""
Calendar JSON Data Analyzer and Table Structure Generator
分析日历JSON数据并生成表结构
"""

import json
import sqlite3
import csv
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter


class CalendarAnalyzer:
    """日历数据分析器"""
    
    def __init__(self, json_file_path: str):
        """初始化分析器
        
        Args:
            json_file_path: JSON文件路径
        """
        self.json_file_path = json_file_path
        self.data: List[Dict[str, Any]] = []
        self.output_dir = Path(__file__).parent
        
    def load_data(self) -> None:
        """加载JSON数据"""
        try:
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✅ 成功加载 {len(self.data)} 条记录")
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            raise
    
    def analyze_structure(self) -> Dict[str, Any]:
        """分析数据结构"""
        if not self.data:
            return {}
        
        # 分析字段类型和值
        field_analysis = defaultdict(lambda: {
            'type_counts': Counter(),
            'null_count': 0,
            'sample_values': set(),
            'max_length': 0
        })
        
        for record in self.data:
            for field, value in record.items():
                analysis = field_analysis[field]
                
                if value is None:
                    analysis['null_count'] += 1
                    analysis['type_counts']['NULL'] += 1
                else:
                    value_type = type(value).__name__
                    analysis['type_counts'][value_type] += 1
                    
                    # 收集样本值
                    if len(analysis['sample_values']) < 10:
                        if isinstance(value, (str, int, float, bool)):
                            analysis['sample_values'].add(str(value))
                        elif isinstance(value, dict):
                            analysis['sample_values'].add(str(value))
                    
                    # 计算最大长度
                    if isinstance(value, str):
                        analysis['max_length'] = max(analysis['max_length'], len(value))
                    elif isinstance(value, dict):
                        analysis['max_length'] = max(analysis['max_length'], len(str(value)))
        
        return dict(field_analysis)
    
    def generate_sql_schema(self, analysis: Dict[str, Any]) -> str:
        """生成SQL表结构"""
        sql_lines = [
            "-- Calendar Events Table Schema",
            "-- 日历事件表结构",
            "",
            "CREATE TABLE calendar_events (",
            "    id INTEGER PRIMARY KEY AUTOINCREMENT,"
        ]
        
        for field, info in analysis.items():
            # 确定SQL数据类型
            type_counts = info['type_counts']
            most_common_type = type_counts.most_common(1)[0][0] if type_counts else 'str'
            
            if field == 'params' or most_common_type == 'dict':
                sql_type = "TEXT"  # JSON存储为TEXT
                comment = "-- JSON object"
            elif most_common_type == 'int':
                sql_type = "INTEGER"
                comment = ""
            elif most_common_type == 'str':
                max_len = info['max_length']
                if max_len > 255:
                    sql_type = "TEXT"
                else:
                    sql_type = f"VARCHAR({max(max_len + 50, 255)})"
                comment = f"-- max length: {max_len}"
            elif most_common_type == 'bool':
                sql_type = "BOOLEAN"
                comment = ""
            else:
                sql_type = "TEXT"
                comment = f"-- mixed types: {dict(type_counts)}"
            
            # 是否允许NULL
            null_allowed = "NULL" if info['null_count'] > 0 else "NOT NULL"
            
            sql_lines.append(f"    {field} {sql_type} {null_allowed}, {comment}")
        
        sql_lines.append("    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
        sql_lines.append(");")
        sql_lines.extend([
            "",
            "-- Indexes for better performance",
            "-- 性能优化索引",
            "CREATE INDEX idx_event_type ON calendar_events(eventType);",
            "CREATE INDEX idx_event_category ON calendar_events(eventCategory);",
            "CREATE INDEX idx_date ON calendar_events(year, month, day);",
            "CREATE INDEX idx_weekday ON calendar_events(weekday);",
            ""
        ])
        
        return "\n".join(sql_lines)
    
    def generate_analysis_report(self, analysis: Dict[str, Any]) -> str:
        """生成数据分析报告"""
        report_lines = [
            "# Calendar Data Analysis Report",
            "# 日历数据分析报告",
            "",
            f"## 基本信息",
            f"- 总记录数: {len(self.data)}",
            f"- 字段数量: {len(analysis)}",
            "",
            "## 字段分析",
            ""
        ]
        
        for field, info in analysis.items():
            report_lines.extend([
                f"### {field}",
                f"- 数据类型分布: {dict(info['type_counts'])}",
                f"- NULL值数量: {info['null_count']}",
                f"- 最大长度: {info['max_length']}",
                f"- 样本值: {list(info['sample_values'])[:5]}",
                ""
            ])
        
        # 统计各种事件类型
        event_types = Counter(record.get('eventType') for record in self.data)
        event_categories = Counter(record.get('eventCategory') for record in self.data)
        
        report_lines.extend([
            "## 事件类型统计",
            "",
            "### eventType分布:",
            *[f"- {k}: {v}" for k, v in event_types.most_common()],
            "",
            "### eventCategory分布:",
            *[f"- {k}: {v}" for k, v in event_categories.most_common()],
            ""
        ])
        
        return "\n".join(report_lines)

    def export_to_csv(self) -> str:
        """导出数据到CSV文件"""
        csv_file = self.output_dir / "calendar_events.csv"

        if not self.data:
            return str(csv_file)

        # 获取所有字段名
        all_fields = set()
        for record in self.data:
            all_fields.update(record.keys())

        fieldnames = sorted(all_fields)

        try:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

                for record in self.data:
                    # 处理params字段（JSON对象）
                    row = record.copy()
                    if 'params' in row and isinstance(row['params'], dict):
                        row['params'] = json.dumps(row['params'], ensure_ascii=False)
                    writer.writerow(row)

            print(f"✅ CSV文件已导出: {csv_file}")
            return str(csv_file)
        except Exception as e:
            print(f"❌ CSV导出失败: {e}")
            return ""

    def export_to_sqlite(self, schema_sql: str) -> str:
        """导出数据到SQLite数据库"""
        db_file = self.output_dir / "calendar_events.db"

        try:
            # 删除已存在的数据库文件
            if db_file.exists():
                db_file.unlink()

            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # 创建表结构
            cursor.executescript(schema_sql)

            # 插入数据
            if self.data:
                # 获取字段名（排除id和created_at）
                sample_record = self.data[0]
                fields = [k for k in sample_record.keys()]

                placeholders = ', '.join(['?' for _ in fields])
                insert_sql = f"INSERT INTO calendar_events ({', '.join(fields)}) VALUES ({placeholders})"

                for record in self.data:
                    values = []
                    for field in fields:
                        value = record.get(field)
                        if isinstance(value, dict):
                            value = json.dumps(value, ensure_ascii=False)
                        values.append(value)

                    cursor.execute(insert_sql, values)

            conn.commit()
            conn.close()

            print(f"✅ SQLite数据库已创建: {db_file}")
            return str(db_file)
        except Exception as e:
            print(f"❌ SQLite导出失败: {e}")
            return ""

    def export_separate_tables(self) -> None:
        """导出到分离的表格（按事件类型分组）"""
        if not self.data:
            return

        # 按eventType分组
        grouped_data = defaultdict(list)
        for record in self.data:
            event_type = record.get('eventType', 'unknown')
            grouped_data[event_type].append(record)

        # 为每个类型创建CSV文件
        for event_type, records in grouped_data.items():
            safe_filename = event_type.replace('/', '_').replace('\\', '_')
            csv_file = self.output_dir / f"calendar_{safe_filename}.csv"

            if records:
                all_fields = set()
                for record in records:
                    all_fields.update(record.keys())

                fieldnames = sorted(all_fields)

                try:
                    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.DictWriter(f, fieldnames=fieldnames)
                        writer.writeheader()

                        for record in records:
                            row = record.copy()
                            if 'params' in row and isinstance(row['params'], dict):
                                row['params'] = json.dumps(row['params'], ensure_ascii=False)
                            writer.writerow(row)

                    print(f"✅ 已导出 {event_type}: {csv_file} ({len(records)} 条记录)")
                except Exception as e:
                    print(f"❌ 导出 {event_type} 失败: {e}")

    def run_analysis(self) -> None:
        """运行完整分析流程"""
        print("🚀 开始分析日历数据...")

        # 1. 加载数据
        self.load_data()

        # 2. 分析结构
        print("📊 分析数据结构...")
        analysis = self.analyze_structure()

        # 3. 生成SQL schema
        print("🗄️ 生成SQL表结构...")
        schema_sql = self.generate_sql_schema(analysis)

        # 4. 生成分析报告
        print("📝 生成分析报告...")
        report = self.generate_analysis_report(analysis)

        # 5. 保存文件
        schema_file = self.output_dir / "calendar_schema.sql"
        report_file = self.output_dir / "analysis_report.md"

        with open(schema_file, 'w', encoding='utf-8') as f:
            f.write(schema_sql)
        print(f"✅ SQL结构已保存: {schema_file}")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"✅ 分析报告已保存: {report_file}")

        # 6. 导出数据
        print("📤 导出数据...")
        self.export_to_csv()
        self.export_to_sqlite(schema_sql)
        self.export_separate_tables()

        print("🎉 分析完成！")


def main():
    """主函数"""
    # JSON文件路径（相对于脚本当前目录）
    script_dir = Path(__file__).parent
    repo_root = script_dir.parent.parent
    json_file = repo_root / "apk-res/apk-res/assets/flutter_assets/assets/data/calendar.json"

    # 检查文件是否存在
    if not json_file.exists():
        print(f"❌ 找不到JSON文件: {json_file}")
        print(f"当前工作目录: {os.getcwd()}")
        print(f"脚本目录: {script_dir}")
        print(f"仓库根目录: {repo_root}")
        return

    # 创建分析器并运行
    analyzer = CalendarAnalyzer(str(json_file))
    analyzer.run_analysis()


if __name__ == "__main__":
    main()
